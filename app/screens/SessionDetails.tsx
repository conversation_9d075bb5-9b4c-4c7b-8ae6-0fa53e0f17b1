import React, { useEffect, useState, useCallback } from "react";
import {
  ActivityIndicator,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Pressable,
  Alert,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Session, SessionResult, sessionService } from "@/services/sessionService";
import { SessionResults } from "@/components/SessionResults";
import { useLocalSearchParams, useRouter } from "expo-router";



export default function SessionDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const [session, setSession] = useState<Session | null>(null);
  const [sessionResult, setSessionResult] = useState<SessionResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Enhanced data fetching function
  const fetchSessionData = useCallback(async () => {
    if (!id) {
      setError("شناسه جلسه ارائه نشده است.");
      return;
    }

    try {
      setError(null);

      // Fetch session details
      const sessionData = await sessionService.getSession(id as string);
      setSession(sessionData);

      // Fetch session results if session is completed
      if (sessionData.status === "completed") {
        try {
          const resultsData = await sessionService.getSessionResults(id as string);
          console.log(resultsData)
          setSessionResult(resultsData);
        } catch (resultsErr) {
          console.error("Error fetching session results:", resultsErr);
          // Don't set error for results, just log it
        }
      }
    } catch (err) {
      setError("خطا در دریافت جزئیات جلسه.");
      console.error(err);
    }
  }, [id]);

  // Initial data load
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchSessionData();
      setLoading(false);
    };

    loadData();
  }, [fetchSessionData]);

  // Pull-to-refresh handler
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchSessionData();
    setRefreshing(false);
  }, [fetchSessionData]);

  // Complete session handler
  const handleCompleteSession = useCallback(async () => {
    if (!session || !id) return;

    Alert.alert(
      "تکمیل جلسه",
      "آیا مطمئن هستید که می‌خواهید این جلسه را تکمیل کنید؟",
      [
        { text: "لغو", style: "cancel" },
        {
          text: "تکمیل",
          style: "default",
          onPress: async () => {
            try {
              setActionLoading(true);
              const updatedSession = await sessionService.completeSession(
                id as string
              );
              setSession(updatedSession);
              Alert.alert("موفقیت", "جلسه با موفقیت تکمیل شد.");
            } catch (err) {
              Alert.alert("خطا", "خطا در تکمیل جلسه.");
              console.error(err);
            } finally {
              setActionLoading(false);
            }
          },
        },
      ]
    );
  }, [session, id]);

  // Recalculate score handler
  const handleRecalculateScore = useCallback(async () => {
    if (!session || !id) return;

    Alert.alert(
      "محاسبه مجدد امتیاز",
      "آیا مطمئن هستید که می‌خواهید امتیاز این جلسه را مجدداً محاسبه کنید؟",
      [
        { text: "لغو", style: "cancel" },
        {
          text: "محاسبه",
          style: "default",
          onPress: async () => {
            try {
              setActionLoading(true);
              const updatedSession = await sessionService.recalculateScore(
                id as string
              );
              setSession(updatedSession);
              Alert.alert("موفقیت", "امتیاز با موفقیت محاسبه شد.");
            } catch (err) {
              Alert.alert("خطا", "خطا در محاسبه مجدد امتیاز.");
              console.error(err);
            } finally {
              setActionLoading(false);
            }
          },
        },
      ]
    );
  }, [session, id]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>در حال بارگذاری جزئیات جلسه...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  if (!session) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText>جلسه یافت نشد.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={["#007bff"]}
            tintColor="#007bff"
          />
        }
      >
        <ThemedText style={styles.title}>جزئیات جلسه</ThemedText>

        {/* Session Basic Information */}
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>اطلاعات کلی</ThemedText>

          <ThemedView style={styles.detailItem}>
            <ThemedText style={styles.label}>شناسه:</ThemedText>
            <ThemedText style={styles.value}>{session.id}</ThemedText>
          </ThemedView>

          <ThemedView style={styles.detailItem}>
            <ThemedText style={styles.label}>ایمیل کاربر:</ThemedText>
            <ThemedText style={styles.value}>{session.user?.email}</ThemedText>
          </ThemedView>

          <ThemedView style={styles.detailItem}>
            <ThemedText style={styles.label}>نام آزمون:</ThemedText>
            <ThemedText style={styles.value}>{session.exam.name}</ThemedText>
          </ThemedView>

          <ThemedView style={styles.detailItem}>
            <ThemedText style={styles.label}>زمان شروع:</ThemedText>
            <ThemedText style={styles.value}>
              {new Date(session.start_time).toLocaleString("fa-IR")}
            </ThemedText>
          </ThemedView>

          {session.end_time && (
            <ThemedView style={styles.detailItem}>
              <ThemedText style={styles.label}>زمان پایان:</ThemedText>
              <ThemedText style={styles.value}>
                {new Date(session.end_time).toLocaleString("fa-IR")}
              </ThemedText>
            </ThemedView>
          )}

          <ThemedView style={styles.detailItem}>
            <ThemedText style={styles.label}>وضعیت:</ThemedText>
            <ThemedText style={[styles.value, styles.statusText]}>
              {session.status}
            </ThemedText>
          </ThemedView>

          <ThemedView style={styles.detailItem}>
            <ThemedText style={styles.label}>امتیاز:</ThemedText>
            <ThemedText style={[styles.value, styles.scoreText]}>
              {session.score}
            </ThemedText>
          </ThemedView>

          {session.created_at && (
            <ThemedView style={styles.detailItem}>
              <ThemedText style={styles.label}>تاریخ ایجاد:</ThemedText>
              <ThemedText style={styles.value}>
                {new Date(session.created_at).toLocaleString("fa-IR")}
              </ThemedText>
            </ThemedView>
          )}

          {session.updated_at && (
            <ThemedView style={styles.detailItem}>
              <ThemedText style={styles.label}>تاریخ بروزرسانی:</ThemedText>
              <ThemedText style={styles.value}>
                {new Date(session.updated_at).toLocaleString("fa-IR")}
              </ThemedText>
            </ThemedView>
          )}
        </ThemedView>

        {/* Action Buttons */}
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>عملیات</ThemedText>

          <ThemedView style={styles.actionButtons}>
            {session.status !== "completed" && (
              <Pressable
                style={[
                  styles.actionButton,
                  styles.completeButton,
                  actionLoading && styles.buttonDisabled,
                ]}
                onPress={handleCompleteSession}
                disabled={actionLoading}
              >
                <ThemedText style={styles.actionButtonText}>
                  {actionLoading ? "در حال پردازش..." : "تکمیل جلسه"}
                </ThemedText>
              </Pressable>
            )}

            {session.status === "completed" && (
              <Pressable
                style={[
                  styles.actionButton,
                  styles.recalculateButton,
                  actionLoading && styles.buttonDisabled,
                ]}
                onPress={handleRecalculateScore}
                disabled={actionLoading}
              >
                <ThemedText style={styles.actionButtonText}>
                  {actionLoading ? "در حال محاسبه..." : "محاسبه مجدد امتیاز"}
                </ThemedText>
              </Pressable>
            )}

            {session.status === "completed" && (
              <Pressable
                style={[styles.actionButton, styles.viewAnswersButton]}
                onPress={() => router.push(`/screens/SessionAnswersScreen?id=${id}`)}
              >
                <ThemedText style={styles.actionButtonText}>
                  مشاهده پاسخ‌های ارائه شده
                </ThemedText>
              </Pressable>
            )}
          </ThemedView>
        </ThemedView>

        {/* Session Results */}
        {session.status === "completed" && sessionResult && (
          <ThemedView style={styles.section}>
            <ThemedText style={styles.sectionTitle}>نتایج جلسه</ThemedText>
            <SessionResults sessionResult={sessionResult} />
          </ThemedView>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  scrollView: {
    flex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
    color: "#1a1a1a",
  },
  section: {
    backgroundColor: "#ffffff",
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 16,
    color: "#2c3e50",
    textAlign: "right",
  },
  detailItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  label: {
    fontWeight: "bold",
    fontSize: 16,
    color: "#34495e",
    flex: 1,
    textAlign: "right",
  },
  value: {
    fontSize: 16,
    color: "#2c3e50",
    flex: 2,
    textAlign: "left",
  },
  statusText: {
    fontWeight: "bold",
    color: "#27ae60",
  },
  scoreText: {
    fontWeight: "bold",
    fontSize: 18,
    color: "#e74c3c",
  },
  errorText: {
    color: "#e74c3c",
    fontSize: 16,
    textAlign: "center",
  },
  actionButtons: {
    flexDirection: "column",
    gap: 12,
  },
  actionButton: {
    backgroundColor: "#3498db",
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  completeButton: {
    backgroundColor: "#27ae60",
  },
  recalculateButton: {
    backgroundColor: "#f39c12",
  },
  viewAnswersButton: {
    backgroundColor: "#6c5ce7",
  },
  buttonDisabled: {
    backgroundColor: "#bdc3c7",
    opacity: 0.6,
  },
  actionButtonText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "bold",
  },
  answerItem: {
    backgroundColor: "#f8f9fa",
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: "#3498db",
  },
  answerHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  answerNumber: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2c3e50",
  },
  answerStatus: {
    fontSize: 14,
    fontWeight: "bold",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  correctAnswer: {
    backgroundColor: "#d4edda",
    color: "#155724",
  },
  incorrectAnswer: {
    backgroundColor: "#f8d7da",
    color: "#721c24",
  },
  questionContainer: {
    marginBottom: 12,
  },
  questionLabel: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#6c757d",
    marginBottom: 4,
  },
  questionText: {
    fontSize: 16,
    color: "#2c3e50",
    lineHeight: 24,
    textAlign: "right",
  },
  optionContainer: {
    marginBottom: 12,
  },
  optionLabel: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#6c757d",
    marginBottom: 4,
  },
  optionText: {
    fontSize: 16,
    lineHeight: 22,
    textAlign: "right",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: "#ffffff",
  },
  correctOptionText: {
    borderLeftWidth: 3,
    borderLeftColor: "#28a745",
    backgroundColor: "#d4edda",
  },
  incorrectOptionText: {
    borderLeftWidth: 3,
    borderLeftColor: "#dc3545",
    backgroundColor: "#f8d7da",
  },
  answerMeta: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "#e9ecef",
  },
  metaText: {
    fontSize: 12,
    color: "#6c757d",
  },
});
