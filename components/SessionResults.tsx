import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Card } from '@/components/Card';
import { SessionResult, CourseResult, CourseGroupResult } from '@/services/sessionService';
import { rtlStyle } from '@/utils/rtl';

interface SessionResultsProps {
  sessionResult: SessionResult;
}

export function SessionResults({ sessionResult }: SessionResultsProps) {
  const formatScore = (score: number): string => {
    return score.toFixed(2);
  };

  const renderCourseResult = (courseResult: CourseResult) => (
    <Card key={courseResult.courseId} style={styles.resultCard}>
      <ThemedView style={styles.cardHeader}>
        <ThemedText style={styles.cardTitle}>{courseResult.courseName}</ThemedText>
        <ThemedText style={styles.scoreText}>
          {formatScore(courseResult.courseScore)}%
        </ThemedText>
      </ThemedView>
      
      <ThemedView style={styles.statsContainer}>
        <ThemedView style={styles.statItem}>
          <ThemedText style={styles.statLabel}>کل سوالات:</ThemedText>
          <ThemedText style={styles.statValue}>{courseResult.totalTests}</ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.statItem}>
          <ThemedText style={styles.statLabel}>پاسخ‌های صحیح:</ThemedText>
          <ThemedText style={[styles.statValue, styles.correctText]}>
            {courseResult.correctAnswers}
          </ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.statItem}>
          <ThemedText style={styles.statLabel}>پاسخ‌های نادرست:</ThemedText>
          <ThemedText style={[styles.statValue, styles.incorrectText]}>
            {courseResult.wrongAnswers}
          </ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.statItem}>
          <ThemedText style={styles.statLabel}>بدون پاسخ:</ThemedText>
          <ThemedText style={[styles.statValue, styles.unansweredText]}>
            {courseResult.unanswered}
          </ThemedText>
        </ThemedView>
      </ThemedView>
    </Card>
  );

  const renderCourseGroupResult = (courseGroupResult: CourseGroupResult) => (
    <Card key={courseGroupResult.courseGroupId} style={styles.groupCard}>
      <ThemedView style={styles.cardHeader}>
        <ThemedText style={styles.groupTitle}>{courseGroupResult.courseGroupName}</ThemedText>
      </ThemedView>
      
      <ThemedView style={styles.groupStatsContainer}>
        <ThemedView style={styles.groupStatItem}>
          <ThemedText style={styles.statLabel}>میانگین نمره:</ThemedText>
          <ThemedText style={styles.groupScoreText}>
            {formatScore(courseGroupResult.averageScore)}%
          </ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.groupStatItem}>
          <ThemedText style={styles.statLabel}>میانگین وزنی:</ThemedText>
          <ThemedText style={styles.groupScoreText}>
            {formatScore(courseGroupResult.weightedAverageScore)}%
          </ThemedText>
        </ThemedView>
      </ThemedView>
    </Card>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Overall Score */}
      <Card style={styles.overallScoreCard}>
        <ThemedView style={styles.overallScoreContainer}>
          <ThemedText style={styles.overallScoreLabel}>نمره کلی جلسه</ThemedText>
          <ThemedText style={styles.overallScoreValue}>
            {formatScore(sessionResult.overallSessionScore)}%
          </ThemedText>
        </ThemedView>
      </Card>

      {/* Course Results */}
      <ThemedView style={styles.section}>
        <ThemedText style={styles.sectionTitle}>نتایج درس‌ها</ThemedText>
        {sessionResult.courseResults.map(renderCourseResult)}
      </ThemedView>

      {/* Course Group Results */}
      <ThemedView style={styles.section}>
        <ThemedText style={styles.sectionTitle}>نتایج گروه‌های درسی</ThemedText>
        {sessionResult.courseGroupResults.map(renderCourseGroupResult)}
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
  },
  overallScoreCard: {
    marginBottom: 20,
    backgroundColor: '#4F46E5',
  },
  overallScoreContainer: {
    alignItems: 'center',
    padding: 20,
  },
  overallScoreLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  overallScoreValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: rtlStyle.textAlign,
  },
  resultCard: {
    marginBottom: 12,
  },
  groupCard: {
    marginBottom: 12,
    backgroundColor: '#F8F9FA',
  },
  cardHeader: {
    flexDirection: rtlStyle.flexDirection.row,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
    textAlign: rtlStyle.textAlign,
  },
  groupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: rtlStyle.textAlign,
  },
  scoreText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4F46E5',
  },
  groupScoreText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#059669',
  },
  statsContainer: {
    gap: 8,
  },
  groupStatsContainer: {
    gap: 12,
  },
  statItem: {
    flexDirection: rtlStyle.flexDirection.row,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  groupStatItem: {
    flexDirection: rtlStyle.flexDirection.row,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 14,
    opacity: 0.8,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  correctText: {
    color: '#059669',
  },
  incorrectText: {
    color: '#DC2626',
  },
  unansweredText: {
    color: '#D97706',
  },
});
